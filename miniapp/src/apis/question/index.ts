import request from '@/utils/request';
import type * as T from './model';

// 获取问卷列表
export const getQuestions = (params?: {
  current?: number;
  pageSize?: number;
}) =>
  request<T.IQuestionListResponse>({
    url: '/wechat/getQuestions',
    method: 'GET',
    params,
  });

// 获取问卷详情
export const getQuestionDetail = (questionId: number) =>
  request<T.IQuestionDetailResponse>({
    url: '/wechat/getQuestionDetail',
    method: 'GET',
    params: {
      questionId
    }
  });

// 业主答卷
export const answerAdd = (data: T.IOwnerAnswerRequest) =>
  request<boolean>({
    url: '/wechat/answerAdd',
    method: 'POST',
    data
  });
