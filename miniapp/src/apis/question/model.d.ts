// 问卷相关类型定义

// 问卷项目选项
export interface IQuestionOption {
  optionsId: string | number;
  optionContent: string;
  sort: number;
  housingId: string | number;
  housingName: string;
}

// 问卷项目
export interface IQuestionItem {
  itemId: string | number;
  itemTitle: string;
  itemType: number;
  questionId: string | number;
  sort: number;
}

// 问卷信息
export interface IQuestion {
  questionId: number;
  questionTitle: string;
  questionRemark: string;
  signature: number;
  housingId: number;
  createTime: string;
  questionItems: IQuestionItem[];
  housingName: string;
  questionOptions: IQuestionOption[];
  isVoted: number; // 0-未投票 1-已投票
  questionStartTime: string;
  questionEndTime: string;
}

// 问卷列表响应
export interface IQuestionListResponse {
  rows: IQuestion[];
  total: number;
}

// 问卷详情响应
export interface IQuestionDetailResponse extends IQuestion {}

// 业主答卷请求参数
export interface IOwnerAnswerRequest {
  questionId: number;
  answers: {
    itemId: number;
    answer: string;
  }[];
}
