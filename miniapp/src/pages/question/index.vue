<template>
  <scroll-view :class="styles.myContainer" class="pageIn" scroll-y="true">
    <navbar title="问卷调查" background-color="#f5f5f9" />

    <view class="question-list">
      <view
        v-for="item in data.list"
        :key="item.questionId"
        class="question-item"
        @tap="toQuestionDetail(item)"
      >
        <view class="question-header">
          <view class="question-title">{{ item.questionTitle }}</view>
          <view class="vote-status" :class="item.isVoted ? 'voted' : 'not-voted'">
            {{ item.isVoted ? '已参与' : '未参与' }}
          </view>
        </view>

        <view class="question-description">
          {{ item.questionRemark }}
        </view>

        <view class="question-time">
          <view class="time-item">
            <text class="time-label">开始时间：</text>
            <text class="time-value">{{ formatTime(item.questionStartTime) }}</text>
          </view>
          <view class="time-item">
            <text class="time-label">结束时间：</text>
            <text class="time-value">{{ formatTime(item.questionEndTime) }}</text>
          </view>
        </view>

        <view class="question-footer">
          <view class="housing-name">{{ item.housingName }}</view>
          <view class="question-action" :class="getActionClass(item)">
            {{ getActionText(item) }}
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="data.list.length === 0 && !data.loading" class="empty-state">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无问卷调查</view>
      </view>

      <!-- 加载状态 -->
      <view v-if="data.loading" class="loading-state">
        <view class="loading-text">加载中...</view>
      </view>
    </view>

    <!-- toast提示 -->
    <my-toast-components ref="myToast" :duration="2500" />
  </scroll-view>
</template>

<script lang="ts" setup>
import { Navbar } from '@fishui/taro-vue';
// @ts-ignore
import styles from './styles.scss';
import { reactive, ref } from 'vue';
import myToastComponents from '@/components/myToast/index.vue';
import { getQuestions } from '@/apis/question';
import type { IQuestion } from '@/apis/question/model';
import Taro, { useDidShow } from '@tarojs/taro';
import dayjs from 'dayjs';

definePageConfig({ backgroundColor: '#f5f5f9' });

const myToast = ref<any>();

const data = reactive({
  list: [] as IQuestion[],
  loading: false,
});

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm');
};

// 跳转到问卷详情
const toQuestionDetail = (item: IQuestion) => {
  // 这里可以跳转到问卷详情页面或者直接进行答题
  Taro.showToast({
    title: `点击了问卷：${item.questionTitle}`,
    icon: 'none',
    duration: 2000
  });
};

// 获取问卷列表
const getQuestionList = async () => {
  try {
    data.loading = true;
    const res = await getQuestions({
      current: 1,
      pageSize: 20
    });

    if (res && res.rows) {
      data.list = res.rows;
    } else if (Array.isArray(res)) {
      // 如果直接返回数组
      data.list = res;
    }
  } catch (error) {
    console.error('获取问卷列表失败:', error);
    myToast.value?.showToast('获取问卷列表失败');
  } finally {
    data.loading = false;
  }
};

// 页面显示时刷新数据
useDidShow(() => {
  getQuestionList();
});

// 初始化加载
getQuestionList();
</script>
