page {
  background: #f5f5f9;
}

.myContainer {
  width: 100vw;
  height: 100vh;

  :global {
    .question-list {
      padding: 20px;
      
      .question-item {
        background: #ffffff;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        
        &:active {
          transform: scale(0.98);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .question-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;
          
          .question-title {
            flex: 1;
            font-size: 32px;
            font-weight: 600;
            color: #333333;
            line-height: 1.4;
            margin-right: 16px;
          }
          
          .vote-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 24px;
            font-weight: 500;
            white-space: nowrap;
            
            &.voted {
              background: #e8f5e8;
              color: #52c41a;
            }
            
            &.not-voted {
              background: #fff2e8;
              color: #fa8c16;
            }
          }
        }
        
        .question-description {
          font-size: 28px;
          color: #666666;
          line-height: 1.5;
          margin-bottom: 20px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .question-time {
          margin-bottom: 16px;
          
          .time-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 24px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-label {
              color: #999999;
              margin-right: 8px;
              min-width: 120px;
            }
            
            .time-value {
              color: #666666;
              font-weight: 500;
            }
          }
        }
        
        .question-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;
          
          .housing-name {
            font-size: 24px;
            color: #999999;
          }
          
          .arrow-icon {
            font-size: 28px;
            color: #cccccc;
            font-weight: bold;
          }
        }
      }
    }
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80px 20px;
      
      .empty-icon {
        font-size: 80px;
        margin-bottom: 20px;
        opacity: 0.6;
      }
      
      .empty-text {
        font-size: 28px;
        color: #999999;
      }
    }
    
    .loading-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 20px;
      
      .loading-text {
        font-size: 28px;
        color: #999999;
      }
    }
  }
}
