page {
  background: #f5f5f9;
}

.myContainer {
  width: 100vw;
  height: 100vh;

  :global {
    .question-list {
      padding: 15px;

      .question-item {
        background: #ffffff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        transition: all 0.2s ease;
        min-height: 120px;

        &:active {
          transform: scale(0.99);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
        }

        .question-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 10px;

          .question-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #333333;
            line-height: 1.3;
            margin-right: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
            max-height: 41.6px; /* 16px * 1.3 * 2 = 41.6px */
          }

          .vote-status {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;

            &.voted {
              background: #e8f5e8;
              color: #52c41a;
            }

            &.not-voted {
              background: #fff2e8;
              color: #fa8c16;
            }
          }
        }

        .question-description {
          font-size: 14px;
          color: #666666;
          line-height: 1.4;
          margin-bottom: 12px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          max-height: 39.2px; /* 14px * 1.4 * 2 = 39.2px */
        }

        .question-time {
          margin-bottom: 12px;

          .time-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .time-label {
              color: #999999;
              margin-right: 6px;
              min-width: 60px;
              flex-shrink: 0;
            }

            .time-value {
              color: #666666;
              font-weight: 400;
            }
          }
        }

        .question-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 10px;
          border-top: 1px solid #f0f0f0;

          .housing-name {
            font-size: 12px;
            color: #999999;
          }

          .arrow-icon {
            font-size: 14px;
            color: #cccccc;
            font-weight: bold;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
      }

      .empty-text {
        font-size: 14px;
        color: #999999;
      }
    }

    .loading-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30px 20px;

      .loading-text {
        font-size: 14px;
        color: #999999;
      }
    }
  }
}
