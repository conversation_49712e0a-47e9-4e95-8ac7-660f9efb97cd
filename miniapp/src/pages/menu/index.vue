<template>
  <scroll-view :class="styles.myContainer" class="pageIn" scroll-y="true" >
    <navbar title="家园议事厅" hide-back background-color="#f5f5f9" />
    <!-- <nut-watermark :gap-x="20" font-color="rgba(0, 0, 0, .1)" :z-index="1" content="家园议事厅" /> -->
    <!-- <nut-noticebar right-icon="circle-close" background="#F1EFFD" color="#333" :speed="35">
      家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、家园议事厅、
    </nut-noticebar> -->

    <nut-swiper :init-page="2" :auto-play="3000" pagination-visible pagination-color="#426543" pagination-unselected-color="#808080">
      <nut-swiper-item v-for="(item, index) in list" :key="index" style="height: 150px">
        <img :src="item" alt="" style="height: 100%; width: 100%" draggable="false" />
      </nut-swiper-item>
    </nut-swiper>

    <view class="menuContent">
      <view class="item vistor" @tap="toVistor">
        <image src="@/assets/images/lpt/tpicon.png"></image>
        业主投票
      </view>
      <!-- <view  class="item photographer" @tap="toPhotographer">
        <image src="@/assets/images/lpt/ggicon.png"></image>
        通知公告
      </view> -->

    </view>
    <view class="menuContent">
      <!-- <view class="item vistor" @tap="toVistor">
        <image src="@/assets/images/lpt/tpicon.png"></image>
        业主投票
      </view> -->
      <view  class="item photographer" @tap="toPhotographer">
        <image src="@/assets/images/lpt/ggicon.png"></image>
        通知公告
      </view>

    </view>

    <side-bar  :showFlags="[6]" />
  </scroll-view>

  <my-toast-components ref="myToast" :duration="2500" />
</template>
<script lang="ts" setup>
// @ts-ignore
import styles
    from './styles.scss';
import {
    ref
} from 'vue';
import {
    Navbar
} from '@fishui/taro-vue';
import {
    useAccountStore
} from '@/stores/account';
import myToastComponents
    from '@/components/myToast/index.vue';
import {
    getInfo
} from '@/apis/mine';
import img1
    from './1.jpg';
import img2
    from './2.jpg';
import img3
    from './3.jpg';
import Taro
    from '@tarojs/taro';
import sideBar
    from '@/components/SideBar/index.vue';

definePageConfig({
  enableShareAppMessage: true,
  enableShareTimeline: true,
});


const account = useAccountStore();


const myToast = ref<any>();


const toVistor = () => {
  Taro.navigateTo({ url: '/pages/ticket/index' });
};
const toPhotographer = () => {
  Taro.navigateTo({ url: '/pages/announcement/index' });
};


const list = ref([
img1,img2,img3
]);

const getUserInfo = async () => {
  const res = await getInfo();
  account.userInfo = res;
};
getUserInfo();
</script>
