export interface VoteVO {
  /**
   * 投票主键
   */
  voteId: string | number;

  /**
   * 投票标题
   */
  voteTitle: string;

  /**
   * 投票标题
   */
  voteCount: number;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 创建人
   */
  createByName: string;

  /**
   * 更新人
   */
  updateByName: string;

  /**
   * 小区主键
   */
  housingId: string | number;

  /**
   * 投票排序
   */
  voteSort: number;

  /**
   * 业主主键
   */
  ownerId?: string;

  /**
   * 选中的选项
   */
  selectedOption?: string;
}

export interface VoteForm extends BaseEntity {
  /**
   * 投票主键
   */
  voteId?: string | number;

  /**
   * 小区主键
   */
  housingId?: string | number;

  /**
   * 议题主键
   */
  topicId?: string | number;

  /**
   * 投票标题
   */
  voteTitle?: string;

  /**
   * 投票排序
   */
  voteSort: number;
}

export interface PaperVoteForm {
  ownerId: string;
  housingId: string | undefined;
  voteList: Array<{
    voteId: string | number;
    voteTitle: string;
    advice: string;
  }>;
}

export interface VoteQuery extends PageQuery {

  /**
   * 议题主键
   */
  topicId?: string | number;

  /**
   * 投票标题
   */
  voteTitle?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



