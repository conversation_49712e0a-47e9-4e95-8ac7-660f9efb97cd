<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="所属小区" prop="housingId">
              <el-select v-model="queryParams.housingId" placeholder="请选择所属小区" clearable>
                <el-option
                  v-for="housing in housingList"
                  :key="housing.housingId"
                  :label="housing.housingName"
                  :value="housing.housingId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态" prop="audit">
              <el-select v-model="queryParams.audit" placeholder="请选择审核状态" clearable>
                <el-option v-for="audit in sys_audits_status" :key="audit.value" :label="audit.label" :value="audit.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="联系人" prop="ownerName">
              <el-input v-model="queryParams.ownerName" placeholder="请输入联系人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="联系号码" prop="ownerPhone">
              <el-input v-model="queryParams.ownerPhone" placeholder="请输入联系号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:owner:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:owner:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:owner:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-dropdown class="mt-[1px]">
              <el-button plain type="info">
                更多
                <el-icon class="el-icon--right"><arrow-down /></el-icon
              ></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item icon="Download" @click="importTemplate">下载模板</el-dropdown-item>
                  <el-dropdown-item icon="Top" @click="handleImport"> 导入数据</el-dropdown-item>
                  <el-dropdown-item icon="Download" @click="handleExport"> 导出数据</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="ownerList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="true" label="业主主键" align="center" prop="ownerId" />
        <el-table-column label="小区名" align="center" prop="housingName" />
        <el-table-column label="房源数" align="center" :show-overflow-tooltip="true" >
          <template #default="scope">
            <router-link :to="'/system/owner/ownerHouse/index/' + scope.row.ownerId" class="link-type">
              <span>{{ scope.row.houseNum }}</span>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="联系人" align="center" prop="ownerName" />
        <el-table-column label="联系号码" align="center" prop="ownerPhone" />
        <el-table-column label="备用电话" align="center" prop="ownerBackPhone" />
        <el-table-column label="审核状态" align="center" prop="audit">
          <template #default="scope">
            <dict-tag :options="sys_audits_status" :value="scope.row.audit" />
          </template>
        </el-table-column>
        <el-table-column label="身份证正面" align="center" prop="idCardZUrl" width="100">
          <template #default="scope">
            <image-preview v-if="scope.row.idCardZUrl" :src="scope.row.idCardZUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="身份证反面" align="center" prop="idCardFUrl" width="100">
          <template #default="scope">
            <image-preview v-if="scope.row.idCardFUrl" :src="scope.row.idCardFUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['system:owner:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['system:owner:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="修改审核状态" placement="top">
              <el-button v-hasPermi="['system:owner:edit']" link type="primary" icon="Check" @click="handleUpdateAuditStatus(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改业主对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="700px" append-to-body>
      <el-form ref="ownerFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属小区" prop="housingId">
          <el-select v-model="form.housingId" placeholder="请选择所属小区" clearable :disabled="!!form.ownerId">
            <el-option v-for="housing in housingList" :key="housing.housingId" :label="housing.housingName" :value="housing.housingId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="ownerName">
          <el-input v-model="form.ownerName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系号码" prop="ownerPhone">
          <el-input v-model="form.ownerPhone" placeholder="请输入联系号码" />
        </el-form-item>
        <el-form-item label="备用电话" prop="ownerBackPhone">
          <el-input v-model="form.ownerBackPhone" placeholder="请输入备用电话" />
        </el-form-item>
        <el-form-item label="组团" prop="cluster">
          <el-input v-model="form.cluster" placeholder="请输入楼幢" />
        </el-form-item>
        <el-form-item label="楼幢" prop="building">
          <el-input v-model="form.building" placeholder="请输入楼幢" />
        </el-form-item>
        <el-form-item label="单元" prop="cell">
          <el-input-number v-model="form.cell" placeholder="请输入单元" />
        </el-form-item>
        <el-form-item label="房号" prop="room">
          <el-input-number v-model="form.room" placeholder="请输入房号" />
        </el-form-item>
        <el-form-item label="面积" prop="acreage">
          <el-input-number v-model="form.acreage" placeholder="请输入面积" />
        </el-form-item>
        <el-form-item label="房产证正面" prop="homeLicenseZ">
          <image-upload v-model="form.homeLicenseZ" :disabled="!!form.ownerId" />
        </el-form-item>
        <el-form-item label="身份证正面" prop="idCardZ">
          <image-upload v-model="form.idCardZ" :disabled="!!form.ownerId" />
        </el-form-item>
        <el-form-item label="身份证反面" prop="idCardF">
          <image-upload v-model="form.idCardF" :disabled="!!form.ownerId" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加修改审核状态的对话框 -->
    <el-dialog v-model="auditDialog.visible" :title="'修改审核状态'" width="500px" append-to-body>
      <el-form ref="auditFormRef" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="audits">
          <el-select v-model="auditForm.audit" placeholder="请选择审核状态">
            <el-option v-for="dict in sys_audits_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitAuditForm">确 定</el-button>
          <el-button @click="cancelAudit">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改业主信息对话框 -->
    <el-dialog v-model="editDialog.visible" :title="editDialog.title" width="500px" append-to-body>
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="80px">
        <el-form-item label="所属小区" prop="housingId">
          <el-select v-model="editForm.housingId" placeholder="请选择所属小区" clearable>
            <el-option
              v-for="housing in housingList"
              :key="housing.housingId"
              :label="housing.housingName"
              :value="housing.housingId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="ownerName">
          <el-input v-model="editForm.ownerName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系号码" prop="ownerPhone">
          <el-input v-model="editForm.ownerPhone" placeholder="请输入联系号码" />
        </el-form-item>
        <el-form-item label="备用电话" prop="ownerBackPhone">
          <el-input v-model="editForm.ownerBackPhone" placeholder="请输入备用电话" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitEditForm">确 定</el-button>
          <el-button @click="cancelEdit">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Owner" lang="ts">
import {
  addOwner,
  delOwner,
  getOwner,
  listOwner,
  updateAudit,
  updateOwner
} from '@/api/system/owner';
import {
  OwnerForm,
  OwnerQuery,
  OwnerVO
} from '@/api/system/owner/types';
import {
  HousingVO
} from '@/api/system/housing/types';
import {
  selectOption
} from '@/api/system/housing';
import {
  globalHeaders
} from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_audits_status } = toRefs<any>(proxy?.useDict('sys_audits_status'));

const housingList = ref<HousingVO[]>([]);
const ownerList = ref<OwnerVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const ownerFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();
const auditFormRef = ref<ElFormInstance>();
const editFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const auditDialog = reactive<DialogOption>({
  visible: false,
  title: '修改审核状态'
});

const editDialog = reactive<DialogOption>({
  visible: false,
  title: '修改业主信息'
});

const auditForm = reactive({
  ownerId: undefined,
  audit: undefined
});

const editForm = reactive<OwnerForm>({
  ownerId: undefined,
  housingId: undefined,
  ownerName: undefined,
  ownerPhone: undefined
});

const auditRules = {
  audit: [{ required: true, message: '审核状态不能为空', trigger: 'change' }]
};

const editRules = {
  housingId: [{ required: true, message: '小区id不能为空', trigger: 'change' }],
  ownerName: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
  ownerPhone: [{ required: true, message: '联系号码不能为空', trigger: 'blur' }]
};

/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/system/owner/importData'
});

const initFormData: OwnerForm = {
  ownerId: undefined,
  housingId: undefined,
  acreage: undefined,
  building: undefined,
  cell: undefined,
  room: undefined,
  ownerName: undefined,
  ownerPhone: undefined,
  homeLicenseZ: undefined,
  remark: undefined,
  idCardZ: undefined,
  idCardF: undefined,
  cluster: undefined,
  ownerBackPhone: undefined
};
const data = reactive<PageData<OwnerForm, OwnerQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    housingId: undefined,
    audit: undefined,
    ownerName: undefined,
    ownerPhone: undefined,
    params: {}
  },
  rules: {
    ownerId: [{ required: true, message: '业主主键不能为空', trigger: 'blur' }],
    housingId: [{ required: true, message: '小区id不能为空', trigger: 'change' }],
    acreage: [{ required: true, message: '面积不能为空', trigger: 'blur' }],
    building: [{ required: true, message: '楼幢不能为空', trigger: 'blur' }],
    cell: [{ required: true, message: '单元不能为空', trigger: 'blur' }],
    room: [{ required: true, message: '房号不能为空', trigger: 'blur' }],
    ownerName: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
    ownerPhone: [{ required: true, message: '联系号码不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询业主列表 */
const getList = async () => {
  loading.value = true;
  const res = await listOwner(queryParams.value);
  ownerList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

const getHousingList = async () => {
  const res = await selectOption();
  housingList.value = res.data;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  ownerFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OwnerVO[]) => {
  ids.value = selection.map((item) => item.ownerId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  getHousingList();
  dialog.visible = true;
  dialog.title = '添加业主';
};

/** 提交按钮 */
const submitForm = () => {
  ownerFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.ownerId) {
        await updateOwner(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addOwner(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: OwnerVO) => {
  const _ownerIds = row?.ownerId || ids.value;
  await proxy?.$modal.confirm('是否确认删除业主编号为"' + _ownerIds + '"的数据项？').finally(() => (loading.value = false));
  await delOwner(_ownerIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/owner/export',
    {
      ...queryParams.value
    },
    `owner_${new Date().getTime()}.xlsx`
  );
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('system/owner/importTemplate', {}, `业主模板_${new Date().getTime()}.xlsx`);
};

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '业主导入';
  upload.open = true;
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

/** 修改审核状态按钮操作 */
const handleUpdateAuditStatus = (row: OwnerVO) => {
  auditForm.ownerId = row.ownerId;
  auditForm.audit = row.audit;
  auditDialog.visible = true;
};

/** 取消修改审核状态 */
const cancelAudit = () => {
  auditDialog.visible = false;
  auditForm.ownerId = undefined;
  auditForm.audit = undefined;
  auditFormRef.value?.resetFields();
};

/** 提交审核状态修改 */
const submitAuditForm = () => {
  auditFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        await updateAudit(auditForm);
        proxy?.$modal.msgSuccess('审核状态修改成功');
        auditDialog.visible = false;
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('审核状态修改失败');
        console.error('Error updating audit status:', error);
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row?: OwnerVO) => {
  const _ownerId = row?.ownerId || ids.value[0];
  await getHousingList();
  const res = await getOwner(_ownerId);
  editForm.ownerId = res.data.ownerId;
  editForm.housingId = res.data.housingId;
  editForm.ownerName = res.data.ownerName;
  editForm.ownerPhone = res.data.ownerPhone;
  editDialog.visible = true;
};

/** 取消编辑 */
const cancelEdit = () => {
  editDialog.visible = false;
  editFormRef.value?.resetFields();
};

/** 提交编辑表单 */
const submitEditForm = () => {
  editFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        await updateOwner(editForm);
        proxy?.$modal.msgSuccess('修改成功');
        editDialog.visible = false;
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('修改失败');
        console.error('Error updating owner:', error);
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

onMounted(() => {
  getList();
  getHousingList();
});
</script>
