<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="短信签名" prop="smsChannelId">
              <el-select v-model="queryParams.smsChannelId" placeholder="请选择短信签名" clearable>
                <el-option
                  v-for="channel in channelList"
                  :key="channel.channelId"
                  :label="channel.channelSign"
                  :value="channel.channelId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="短信编号" prop="smsCode">
              <el-input v-model="queryParams.smsCode" placeholder="请输入短信编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="短信名称" prop="smsName">
              <el-input v-model="queryParams.smsName" placeholder="请输入短信名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="短信类型" prop="smsType">
              <el-select v-model="queryParams.smsType" placeholder="请选择短信类型" clearable >
                <el-option v-for="dict in sys_sms_type" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:smsTemplate:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:smsTemplate:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:smsTemplate:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:smsTemplate:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="smsTemplateList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="短信主键" align="center" prop="smsId" v-if="false" />
        <el-table-column label="短信编号" align="center" prop="smsCode" />
        <el-table-column label="短信签名" align="center" prop="smsChannelSign" />
        <el-table-column label="短信名称" align="center" prop="smsName" />
        <el-table-column label="短信类型" align="center" prop="smsType">
          <template #default="scope">
            <dict-tag :options="sys_sms_type" :value="scope.row.smsType"/>
          </template>
        </el-table-column>
        <el-table-column label="渠道模板id" align="center" prop="smsTemplateId" />
        <el-table-column label="模板内容" align="center" prop="smsContent" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:smsTemplate:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:smsTemplate:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改短信配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="smsTemplateFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="短信标识" prop="smsCode">
          <el-input v-model="form.smsCode" placeholder="请输入短信标识" />
        </el-form-item>
        <el-form-item label="短信名称" prop="smsName">
          <el-input v-model="form.smsName" placeholder="请输入短信名称" />
        </el-form-item>
        <el-form-item label="短信签名" prop="smsChannelId">
          <el-select v-model="form.smsChannelId" placeholder="请选择短信签名">
            <el-option
              v-for="channel in channelList"
              :key="channel.channelId"
              :label="channel.channelSign"
              :value="channel.channelId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信类型" prop="smsType">
          <el-select v-model="form.smsType" placeholder="请选择短信类型">
            <el-option
                v-for="dict in sys_sms_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道模板id" prop="smsTemplateId" label-width="100px">
          <el-input v-model="form.smsTemplateId" placeholder="请输入渠道模板id" />
        </el-form-item>
        <el-form-item label="模板内容" prop="smsContent">
          <editor v-model="form.smsContent" :min-height="192"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SmsTemplate" lang="ts">
import {
  SmsTemplateForm,
  SmsTemplateQuery,
  SmsTemplateVO
} from '@/api/system/smsTemplate/types';
import {
  addSmsTemplate,
  delSmsTemplate,
  getSmsTemplate,
  listSmsTemplate,
  updateSmsTemplate
} from "@/api/system/smsTemplate";
import {
  selectOption
} from "@/api/system/smsChannel";
import {
  SmsChannelVO
} from "@/api/system/smsChannel/types";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_sms_type } = toRefs<any>(proxy?.useDict('sys_sms_type'));

const channelList = ref<SmsChannelVO[]>([]);
const smsTemplateList = ref<SmsTemplateVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const smsTemplateFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SmsTemplateForm = {
  smsId: undefined,
  smsCode: undefined,
  smsName: undefined,
  smsType: undefined,
  smsChannelId: undefined,
  smsTemplateId: undefined,
  smsContent: undefined
}
const data = reactive<PageData<SmsTemplateForm, SmsTemplateQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    smsCode: undefined,
    smsName: undefined,
    smsType: undefined,
    smsChannelId: undefined,
    smsTemplateId: undefined,
    smsContent: undefined,
    params: {
    }
  },
  rules: {
    smsId: [
      { required: true, message: "短信主键不能为空", trigger: "blur" }
    ],
    smsCode: [
      { required: true, message: "短信标识不能为空", trigger: "blur" }
    ],
    smsName: [
      { required: true, message: "短信名称不能为空", trigger: "blur" }
    ],
    smsType: [
      { required: true, message: "短信类型不能为空", trigger: "change" }
    ],
    smsChannelId: [
      { required: true, message: "短信签名不能为空", trigger: "blur" }
    ],
    smsTemplateId: [
      { required: true, message: "渠道模板id不能为空", trigger: "blur" }
    ],
    smsContent: [
      { required: true, message: "模板内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询短信配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSmsTemplate(queryParams.value);
  smsTemplateList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  smsTemplateFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: SmsTemplateVO[]) => {
  ids.value = selection.map(item => item.smsId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  getChannelList();
  reset();
  dialog.visible = true;
  dialog.title = "添加短信配置";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: SmsTemplateVO) => {
  await getChannelList();
  reset();
  const _smsId = row?.smsId || ids.value[0]
  const res = await getSmsTemplate(_smsId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改短信配置";
}

/** 提交按钮 */
const submitForm = () => {
  smsTemplateFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.smsId) {
        await updateSmsTemplate(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addSmsTemplate(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: SmsTemplateVO) => {
  const _smsIds = row?.smsId || ids.value;
  await proxy?.$modal.confirm('是否确认删除短信配置编号为"' + _smsIds + '"的数据项？').finally(() => loading.value = false);
  await delSmsTemplate(_smsIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/smsTemplate/export', {
    ...queryParams.value
  }, `smsTemplate_${new Date().getTime()}.xlsx`)
}

const getChannelList = async () => {
  const res = await selectOption();
  channelList.value = res.data;
};

onMounted(() => {
  getList();
  getChannelList();
});
</script>
