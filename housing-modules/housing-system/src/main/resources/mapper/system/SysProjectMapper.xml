<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.housing.system.mapper.SysProjectMapper">

    <resultMap type="org.housing.system.domain.vo.SysProjectVo" id="SysProjectResult">
    </resultMap>

    <select id="selectPageProjectList" resultMap="SysProjectResult">
        SELECT
            p.*,
            n.notice_title,
            n.notice_content,
            h.housing_name,
            h.housing_size
        FROM
            sys_project p
            JOIN sys_notice n ON p.notice_id = n.notice_id
            AND n.`status` = '0'
            JOIN sys_housing h ON h.housing_id = n.housing_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectProjectList" resultMap="SysProjectResult">
        SELECT
            p.*,
            n.notice_title,
            n.notice_content,
            h.housing_name,
            h.housing_size
        FROM
            sys_project p
            JOIN sys_notice n ON p.notice_id = n.notice_id
            AND n.`status` = '0'
            JOIN sys_housing h ON h.housing_id = n.housing_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectProjectById" resultMap="SysProjectResult">
        SELECT
            p.*,
            n.notice_title,
            n.notice_content,
            h.housing_name,
            h.housing_size
        FROM
            sys_project p
            JOIN sys_notice n ON p.notice_id = n.notice_id
            AND n.`status` = '0'
            JOIN sys_housing h ON h.housing_id = n.housing_id
        WHERE
            p.project_id = #{projectId}
    </select>
</mapper>
