<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.housing.system.mapper.SysOwnerHouseMapper">

    <select id="selectOwnerHouse" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            h.owner_id,
            o.housing_id,
            o.owner_name,
            o.owner_phone,
            GROUP_CONCAT(h.owner_house_id SEPARATOR '-') AS house_info
        FROM
            `sys_owner_house` h
                JOIN sys_owner o ON o.owner_id = h.owner_id
        GROUP BY
            h.owner_id
        HAVING
            COUNT(*) > 1
    </select>
</mapper>
