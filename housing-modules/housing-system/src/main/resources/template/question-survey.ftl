<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业主意见征询表</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }

        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            background: white;
        }

        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            page-break-after: always;
            padding: 20mm;
            box-sizing: border-box;
        }

        .page:last-child {
            page-break-after: avoid;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .basic-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .basic-info span {
            flex: 1;
        }

        .owner-info {
            margin-bottom: 30px;
            font-size: 14px;
        }

        .satisfaction-section {
            margin-bottom: 30px;
        }

        .satisfaction-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .satisfaction-table th,
        .satisfaction-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 12px;
        }

        .satisfaction-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .satisfaction-table .item-name {
            text-align: left;
            width: 120px;
        }

        .checkbox {
            width: 15px;
            height: 15px;
            border: 1px solid #000;
            display: inline-block;
            text-align: center;
            line-height: 13px;
            margin: 0 2px;
        }

        .checkbox.checked {
            background-color: #000;
            color: white;
        }

        .checkbox.checked::before {
            content: "✓";
            font-weight: bold;
        }

        .question-section {
            margin-bottom: 20px;
        }

        .question-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .question-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
        }

        .question-content {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .options {
            margin-left: 20px;
        }

        .option-item {
            margin-bottom: 5px;
        }

        .text-answer {
            border-bottom: 1px solid #000;
            min-height: 20px;
            padding: 5px;
            margin-top: 5px;
        }

        .signature-section {
            margin-top: 40px;
        }

        .signature-item {
            margin-bottom: 30px;
            position: relative;
        }

        .signature-label {
            display: inline-block;
            background: white;
            padding-right: 10px;
            position: relative;
            z-index: 1;
        }

        .signature-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            border-bottom: 1px solid #000;
        }

        .date-section {
            text-align: right;
            margin-top: 30px;
        }

        .underline {
            border-bottom: 1px solid #000;
            display: inline-block;
            min-width: 100px;
            text-align: center;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
            .page {
                box-shadow: none;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <#list dataList as data>
    <div class="page">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">${data.questionTitle}</div>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
            <span>物业名称：<span class="underline">${data.housingName!''}</span></span>
            <span>房号：<span class="underline">${data.houseInfo!''}</span></span>
            <span>联系方式：<span class="underline">${data.ownerPhone!''}</span></span>
        </div>

        <!-- 满意度调查表格 -->
        <#if data.questionOptions?? && data.questionOptions?trim != ''>
        <div class="satisfaction-section">
            <#assign options = data.questionOptions?split(',')>
            <table class="satisfaction-table">
                <thead>
                    <tr>
                        <th rowspan="2"></th>
                        <#list options as option>
                        <th>${option}</th>
                        </#list>
                    </tr>
                </thead>
                <tbody>
                    <#list data.questionItems as item>
                    <tr>
                        <td class="item-name">${item.sort}.${item.content}</td>
                        <#list options as option>
                        <td>
                            <#assign isChecked = false>
                            <#if data.answersInfos??>
                                <#list data.answersInfos as answer>
                                    <#if answer.itemContent == item.content>
                                        <#if item.itemType == 1 && answer.optionValue?? && answer.optionValue == option_index + 1>
                                            <#assign isChecked = true>
                                        <#elseif item.itemType == 3 && answer.optionValue??>
                                            <#-- 多选题处理 -->
                                            <#assign optionValues = answer.optionValue?string?split(',')>
                                            <#list optionValues as optVal>
                                                <#if optVal?trim == (option_index + 1)?string>
                                                    <#assign isChecked = true>
                                                </#if>
                                            </#list>
                                        </#if>
                                    </#if>
                                </#list>
                            </#if>
                            <span class="checkbox <#if isChecked>checked</#if>"></span>
                        </td>
                        </#list>
                    </tr>
                    </#list>
                </tbody>
            </table>
        </div>
        </#if>

        <!-- 问题详情 -->
        <div class="question-section">
            <#list data.questionItems as item>
                <#if item.itemType == 2>
                <div class="question-item">
                    <div class="question-content">${item.sort}. ${item.content}</div>
                    <div class="text-answer">
                        <#if data.answersInfos??>
                            <#list data.answersInfos as answer>
                                <#if answer.itemContent == item.content && answer.textAnswer??>
                                    ${answer.textAnswer}
                                </#if>
                            </#list>
                        </#if>
                    </div>
                </div>
                </#if>
            </#list>
        </div>



        <!-- 签名区域 -->
        <#if data.signature == 1>
        <div class="signature-section">
            <div class="signature-item">
                <span class="signature-label">物业服务中心工作人员：</span>
                <div class="signature-line"></div>
            </div>
            <div class="signature-item">
                <span class="signature-label">业主本人签名：</span>
                <div class="signature-line"></div>
            </div>
        </div>
        </#if>

        <!-- 日期 -->
        <div class="date-section">
            <#if data.answerDate??>
                <span style="margin-right: 50px;">${data.answerDate?string('yyyy')}年</span>
                <span style="margin-right: 50px;">${data.answerDate?string('MM')}月</span>
                <span>${data.answerDate?string('dd')}日</span>
            <#else>
                <span style="margin-right: 50px;">年</span>
                <span style="margin-right: 50px;">月</span>
                <span>日</span>
            </#if>
        </div>
    </div>
    </#list>
</body>
</html>
