package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 答卷详情对象 sys_answer_details
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@Data
@TableName("sys_question_answer_details")
public class SysQuestionAnswerDetails implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 答题详情ID
     */
    @TableId(value = "answer_details_id")
    private Long answerDetailsId;

    /**
     * 关联答卷主表ID
     */
    private Long answerId;

    /**
     * 关联题目ID
     */
    private Long itemId;

    /**
     * 选项id 英文逗号隔开
     */
    private String optionAnswer;

    /**
     * 填空答案
     */
    private String textAnswer;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
