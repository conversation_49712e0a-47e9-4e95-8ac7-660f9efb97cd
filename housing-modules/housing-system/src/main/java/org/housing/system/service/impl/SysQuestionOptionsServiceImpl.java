package org.housing.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.SysQuestionOptions;
import org.housing.system.domain.bo.SysQuestionOptionsBo;
import org.housing.system.domain.vo.SysQuestionOptionsVo;
import org.housing.system.mapper.SysQuestionOptionsMapper;
import org.housing.system.service.ISysQuestionOptionsService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 选择题选项Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@RequiredArgsConstructor
@Service
public class SysQuestionOptionsServiceImpl implements ISysQuestionOptionsService {

    private final SysQuestionOptionsMapper baseMapper;

    /**
     * 查询选择题选项
     *
     * @param itemOptionsId 主键
     * @return 选择题选项
     */
    @Override
    public SysQuestionOptionsVo queryById(Long itemOptionsId){
        return baseMapper.selectVoById(itemOptionsId);
    }

    /**
     * 分页查询选择题选项列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 选择题选项分页列表
     */
    @Override
    public TableDataInfo<SysQuestionOptionsVo> queryPageList(SysQuestionOptionsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysQuestionOptions> lqw = buildQueryWrapper(bo);
        Page<SysQuestionOptionsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的选择题选项列表
     *
     * @param bo 查询条件
     * @return 选择题选项列表
     */
    @Override
    public List<SysQuestionOptionsVo> queryList(SysQuestionOptionsBo bo) {
        LambdaQueryWrapper<SysQuestionOptions> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysQuestionOptions> buildQueryWrapper(SysQuestionOptionsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysQuestionOptions> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOptionContent()), SysQuestionOptions::getOptionContent, bo.getOptionContent());
        lqw.eq(bo.getSort() != null, SysQuestionOptions::getSort, bo.getSort());
        lqw.orderByAsc(SysQuestionOptions::getSort);
        return lqw;
    }

    /**
     * 新增选择题选项
     *
     * @param bo 选择题选项
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysQuestionOptionsBo bo) {
        SysQuestionOptions add = MapstructUtils.convert(bo, SysQuestionOptions.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOptionsId(add.getOptionsId());
        }
        return flag;
    }

    /**
     * 修改选择题选项
     *
     * @param bo 选择题选项
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysQuestionOptionsBo bo) {
        SysQuestionOptions update = MapstructUtils.convert(bo, SysQuestionOptions.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysQuestionOptions entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除选择题选项信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
