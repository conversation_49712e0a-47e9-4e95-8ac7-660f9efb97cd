package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 业主答卷主对象 sys_question_answers
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@Data
@TableName("sys_question_answers")
public class SysQuestionAnswers implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 答卷ID
     */
    @TableId(value = "answers_id")
    private Long answersId;

    /**
     * 关联问卷主表ID
     */
    private Long questionId;

    /**
     * 小区主键
     */
    private Long housingId;

    /**
     * 业主唯一标识
     */
    private Long ownerId;

    /**
     * 电子签名图片路径
     */
    private Long eSignImg;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 租户编号
     */
    private String tenantId;
}
