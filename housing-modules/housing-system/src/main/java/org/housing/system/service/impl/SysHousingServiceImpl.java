package org.housing.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.constant.CacheNames;
import org.housing.common.core.exception.ServiceException;
import org.housing.common.core.service.HousingService;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.SpringUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.SysHousing;
import org.housing.system.domain.bo.SysHousingBo;
import org.housing.system.domain.vo.SysHousingVo;
import org.housing.system.mapper.SysHousingMapper;
import org.housing.system.service.ISysHousingService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小区Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@RequiredArgsConstructor
@Service
public class SysHousingServiceImpl implements ISysHousingService, HousingService {

    private final SysHousingMapper baseMapper;

    /**
     * 查询小区
     *
     * @param housingId 主键
     * @return 小区
     */
    @Cacheable(cacheNames = CacheNames.SYS_HOUSING, key = "#housingId")
    @Override
    public SysHousingVo queryById(Long housingId){
        return baseMapper.selectVoById(housingId);
    }

    /**
     * 分页查询小区列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 小区分页列表
     */
    @Override
    public TableDataInfo<SysHousingVo> queryPageList(SysHousingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysHousing> lqw = buildQueryWrapper(bo);
        Page<SysHousingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的小区列表
     *
     * @param bo 查询条件
     * @return 小区列表
     */
    @Override
    public List<SysHousingVo> queryList(SysHousingBo bo) {
        LambdaQueryWrapper<SysHousing> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysHousing> buildQueryWrapper(SysHousingBo bo) {
        LambdaQueryWrapper<SysHousing> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getHousingName()), SysHousing::getHousingName, bo.getHousingName());
        lqw.like(StringUtils.isNotBlank(bo.getHousingSize()), SysHousing::getHousingSize, bo.getHousingSize());
        lqw.like(StringUtils.isNotBlank(bo.getProvince()), SysHousing::getProvince, bo.getProvince());
        lqw.like(StringUtils.isNotBlank(bo.getCity()), SysHousing::getCity, bo.getCity());
        lqw.like(StringUtils.isNotBlank(bo.getArea()), SysHousing::getArea, bo.getArea());
        lqw.like(StringUtils.isNotBlank(bo.getAddress()), SysHousing::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), SysHousing::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增小区
     *
     * @param bo 小区
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysHousingBo bo) {
        SysHousing add = MapstructUtils.convert(bo, SysHousing.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setHousingId(add.getHousingId());
        }
        return flag;
    }

    /**
     * 修改小区
     *
     * @param bo 小区
     * @return 是否修改成功
     */
    @CacheEvict(cacheNames = CacheNames.SYS_HOUSING, key = "#bo.housingId")
    @Override
    public Boolean updateByBo(SysHousingBo bo) {
        SysHousingVo sysHousingVo = baseMapper.selectVoById(bo.getHousingId());
        if (ObjectUtil.isNull(sysHousingVo)) {
            throw new ServiceException("小区数据不存在");
        }
        SysHousing update = MapstructUtils.convert(bo, SysHousing.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysHousing entity){
        SysHousingVo sysHousingVo = baseMapper.selectVoOne(new LambdaQueryWrapper<SysHousing>().eq(SysHousing::getHousingName, entity.getHousingName()));
        if (sysHousingVo != null) {
            throw new ServiceException("新增的小区'" + entity.getHousingName() + "'失败，名称已存在");
        }
    }

    /**
     * 校验并批量删除小区信息
     *
     * @param id     待删除的主键
     * @return 是否删除成功
     */
    @CacheEvict(cacheNames = CacheNames.SYS_HOUSING, key = "#id")
    @Override
    public Boolean deleteById(Long id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public String selectHousingNameByIds(Long housingId) {
        SysHousingVo sysHousingVo = SpringUtils.getAopProxy(this).queryById(housingId);
        return sysHousingVo == null ? "" : sysHousingVo.getHousingName();
    }

    @Override
    public Long selectHousingIdByName(String housingName) {
        SysHousingVo sysHousingVo = baseMapper.selectVoOne(new LambdaQueryWrapper<SysHousing>().eq(SysHousing::getHousingName, housingName));
        if (sysHousingVo == null) {
            throw new ServiceException("小区名称查不到数据：" + housingName);
        }
        return sysHousingVo.getHousingId();
    }

    @Override
    public List<String> selectAllNames() {
        return baseMapper.selectList().stream().map(SysHousing::getHousingName).toList();
    }
}
