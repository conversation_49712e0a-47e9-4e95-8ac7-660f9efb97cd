package org.housing.system.service;

import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.bo.SysSmsChannelBo;
import org.housing.system.domain.vo.SysSmsChannelVo;

import java.util.Collection;
import java.util.List;

/**
 * 短信渠道Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-03-13
 */
public interface ISysSmsChannelService {

    /**
     * 查询短信渠道
     *
     * @param channelId 主键
     * @return 短信渠道
     */
    SysSmsChannelVo queryById(Long channelId);

    /**
     * 分页查询短信渠道列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 短信渠道分页列表
     */
    TableDataInfo<SysSmsChannelVo> queryPageList(SysSmsChannelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的短信渠道列表
     *
     * @param bo 查询条件
     * @return 短信渠道列表
     */
    List<SysSmsChannelVo> queryList(SysSmsChannelBo bo);

    /**
     * 新增短信渠道
     *
     * @param bo 短信渠道
     * @return 是否新增成功
     */
    Boolean insertByBo(SysSmsChannelBo bo);

    /**
     * 修改短信渠道
     *
     * @param bo 短信渠道
     * @return 是否修改成功
     */
    Boolean updateByBo(SysSmsChannelBo bo);

    /**
     * 校验并批量删除短信渠道信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
