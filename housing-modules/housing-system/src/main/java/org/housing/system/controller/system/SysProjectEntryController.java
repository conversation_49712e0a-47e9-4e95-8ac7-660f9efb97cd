package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysProjectEntryBo;
import org.housing.system.domain.vo.SysProjectEntryVo;
import org.housing.system.service.ISysProjectEntryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目报名
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/projectEntry")
public class SysProjectEntryController extends BaseController {

    private final ISysProjectEntryService sysProjectEntryService;

    /**
     * 查询项目报名列表
     */
    @SaCheckPermission("system:projectEntry:list")
    @GetMapping("/list")
    public TableDataInfo<SysProjectEntryVo> list(SysProjectEntryBo bo, PageQuery pageQuery) {
        return sysProjectEntryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目报名列表
     */
    @SaCheckPermission("system:projectEntry:export")
    @Log(title = "项目报名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysProjectEntryBo bo, HttpServletResponse response) {
        List<SysProjectEntryVo> list = sysProjectEntryService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目报名", SysProjectEntryVo.class, response);
    }

    /**
     * 获取项目报名详细信息
     *
     * @param projectEntryId 主键
     */
    @SaCheckPermission("system:projectEntry:query")
    @GetMapping("/{projectEntryId}")
    public R<SysProjectEntryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long projectEntryId) {
        return R.ok(sysProjectEntryService.queryById(projectEntryId));
    }

    /**
     * 新增项目报名
     */
    @SaCheckPermission("system:projectEntry:add")
    @Log(title = "项目报名", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysProjectEntryBo bo) {
        return toAjax(sysProjectEntryService.insertByBo(bo));
    }

    /**
     * 修改项目报名
     */
    @SaCheckPermission("system:projectEntry:edit")
    @Log(title = "项目报名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysProjectEntryBo bo) {
        return toAjax(sysProjectEntryService.updateByBo(bo));
    }

    /**
     * 修改审核状态
     */
    @SaCheckPermission("system:projectEntry:edit")
    @Log(title = "项目报名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/audits")
    public R<Void> audits(@Validated(EditGroup.class) @RequestBody SysProjectEntryBo bo) {
        return toAjax(sysProjectEntryService.updateAudits(bo));
    }

    /**
     * 删除项目报名
     *
     * @param projectEntryId 主键串
     */
    @SaCheckPermission("system:projectEntry:remove")
    @Log(title = "项目报名", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectEntryId}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long projectEntryId) {
        return toAjax(sysProjectEntryService.deleteById(projectEntryId));
    }
}
