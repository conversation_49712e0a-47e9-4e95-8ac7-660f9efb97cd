package org.housing.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.SysQuestionAnswerDetails;
import org.housing.system.domain.bo.SysQuestionAnswerDetailsBo;
import org.housing.system.domain.vo.SysQuestionAnswerDetailsVo;
import org.housing.system.mapper.SysQuestionAnswerDetailsMapper;
import org.housing.system.service.ISysQuestionAnswerDetailsService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 答卷详情Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@RequiredArgsConstructor
@Service
public class SysQuestionAnswerDetailsServiceImpl implements ISysQuestionAnswerDetailsService {

    private final SysQuestionAnswerDetailsMapper baseMapper;

    /**
     * 查询答卷详情
     *
     * @param answerDetailsId 主键
     * @return 答卷详情
     */
    @Override
    public SysQuestionAnswerDetailsVo queryById(Long answerDetailsId){
        return baseMapper.selectVoById(answerDetailsId);
    }

    /**
     * 分页查询答卷详情列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 答卷详情分页列表
     */
    @Override
    public TableDataInfo<SysQuestionAnswerDetailsVo> queryPageList(SysQuestionAnswerDetailsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysQuestionAnswerDetails> lqw = buildQueryWrapper(bo);
        Page<SysQuestionAnswerDetailsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的答卷详情列表
     *
     * @param bo 查询条件
     * @return 答卷详情列表
     */
    @Override
    public List<SysQuestionAnswerDetailsVo> queryList(SysQuestionAnswerDetailsBo bo) {
        LambdaQueryWrapper<SysQuestionAnswerDetails> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysQuestionAnswerDetails> buildQueryWrapper(SysQuestionAnswerDetailsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysQuestionAnswerDetails> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAnswerId() != null, SysQuestionAnswerDetails::getAnswerId, bo.getAnswerId());
        lqw.eq(bo.getItemId() != null, SysQuestionAnswerDetails::getItemId, bo.getItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getTextAnswer()), SysQuestionAnswerDetails::getTextAnswer, bo.getTextAnswer());
        return lqw;
    }

    /**
     * 新增答卷详情
     *
     * @param bo 答卷详情
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysQuestionAnswerDetailsBo bo) {
        SysQuestionAnswerDetails add = MapstructUtils.convert(bo, SysQuestionAnswerDetails.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAnswerDetailsId(add.getAnswerDetailsId());
        }
        return flag;
    }

    /**
     * 修改答卷详情
     *
     * @param bo 答卷详情
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysQuestionAnswerDetailsBo bo) {
        SysQuestionAnswerDetails update = MapstructUtils.convert(bo, SysQuestionAnswerDetails.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysQuestionAnswerDetails entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除答卷详情信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
