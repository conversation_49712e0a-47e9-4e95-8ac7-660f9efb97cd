package org.housing.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.housing.common.mybatis.core.mapper.BaseMapperPlus;
import org.housing.system.domain.SysOwner;
import org.housing.system.domain.vo.SysOwnerVo;

import java.util.List;

/**
 * 业主Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-28
 */
@Mapper
public interface SysOwnerMapper extends BaseMapperPlus<SysOwner, SysOwnerVo> {

    Page<SysOwnerVo> selectPageOwnerList(@Param("page") Page<SysOwner> page, @Param(Constants.WRAPPER) Wrapper<SysOwner> queryWrapper);

    List<SysOwnerVo> selectOwnerList(@Param(Constants.WRAPPER) Wrapper<SysOwner> queryWrapper);

    SysOwnerVo selectOwnerById(Long ownerId);

    @DS("slave")
    @Select("select * from sys_owner where owner_id = #{ownerId}")
    SysOwner selectBySlave(Long ownerId);
}
