package org.housing.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.housing.system.domain.SysQuestionAnswers;

import java.io.Serial;
import java.io.Serializable;



/**
 * 业主答卷主视图对象 sys_question_answers
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysQuestionAnswers.class)
public class SysQuestionAnswersVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 答卷ID
     */
    @ExcelProperty(value = "答卷ID")
    private Long answersId;

    /**
     * 关联问卷主表ID
     */
    @ExcelProperty(value = "关联问卷主表ID")
    private Long questionId;

    /**
     * 小区主键
     */
    @ExcelProperty(value = "小区主键")
    private Long housingId;

    /**
     * 业主唯一标识
     */
    @ExcelProperty(value = "业主唯一标识")
    private Long ownerId;

    /**
     * 电子签名图片路径
     */
    @ExcelProperty(value = "电子签名图片路径")
    private Long eSignImg;


}
