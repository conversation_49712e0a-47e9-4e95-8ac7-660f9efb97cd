package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 选择题选项对象 sys_question_options
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_question_options")
public class SysQuestionOptions extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 问卷题目选项ID
     */
    @TableId(value = "options_id")
    private Long optionsId;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 选项排序
     */
    private Long sort;

    /**
     * 关联的小区id
     */
    private Long housingId;
}
