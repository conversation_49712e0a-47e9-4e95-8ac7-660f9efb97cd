package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysHousingBo;
import org.housing.system.domain.vo.SysHousingVo;
import org.housing.system.service.ISysHousingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小区
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/housing")
public class SysHousingController extends BaseController {

    private final ISysHousingService sysHousingService;

    /**
     * 查询小区列表
     */
    @SaCheckPermission("system:housing:list")
    @GetMapping("/list")
    public TableDataInfo<SysHousingVo> list(SysHousingBo bo, PageQuery pageQuery) {
        return sysHousingService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询小区下拉列表
     */
    @GetMapping("/selectOption")
    public R<List<SysHousingVo>> list() {
        return R.ok(sysHousingService.queryList(new SysHousingBo()));
    }

    /**
     * 导出小区列表
     */
    @SaCheckPermission("system:housing:export")
    @Log(title = "小区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysHousingBo bo, HttpServletResponse response) {
        List<SysHousingVo> list = sysHousingService.queryList(bo);
        ExcelUtil.exportExcel(list, "小区", SysHousingVo.class, response);
    }

    /**
     * 获取小区详细信息
     *
     * @param housingId 主键
     */
    @SaCheckPermission("system:housing:query")
    @GetMapping("/{housingId}")
    public R<SysHousingVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long housingId) {
        return R.ok(sysHousingService.queryById(housingId));
    }

    /**
     * 新增小区
     */
    @SaCheckPermission("system:housing:add")
    @Log(title = "小区", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysHousingBo bo) {
        return toAjax(sysHousingService.insertByBo(bo));
    }

    /**
     * 修改小区
     */
    @SaCheckPermission("system:housing:edit")
    @Log(title = "小区", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysHousingBo bo) {
        return toAjax(sysHousingService.updateByBo(bo));
    }

    /**
     * 删除小区
     *
     * @param housingId 主键串
     */
    @SaCheckPermission("system:housing:remove")
    @Log(title = "小区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{housingId}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long housingId) {
        return toAjax(sysHousingService.deleteById(housingId));
    }
}
