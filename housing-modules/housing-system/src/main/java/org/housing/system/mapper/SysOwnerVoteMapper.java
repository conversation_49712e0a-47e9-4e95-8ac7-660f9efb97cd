package org.housing.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.housing.common.mybatis.core.mapper.BaseMapperPlus;
import org.housing.system.domain.SysOwnerVote;
import org.housing.system.domain.vo.SysOwnerVoteVo;
import org.housing.system.domain.vo.SysVoteStatisticsVo;

import java.util.List;

/**
 * 业主投票Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysOwnerVoteMapper extends BaseMapperPlus<SysOwnerVote, SysOwnerVoteVo> {

    Page<SysOwnerVoteVo> selectOwnerVotePage(@Param("page") Page<SysOwnerVote> page, @Param(Constants.WRAPPER) Wrapper<SysOwnerVote> queryWrapper);

    List<SysOwnerVoteVo> selectOwnerVoteList(@Param(Constants.WRAPPER) Wrapper<SysOwnerVote> queryWrapper);

    SysOwnerVoteVo selectOwnerVoteById(Long ownerVoteId);

    List<SysVoteStatisticsVo> selectVoteStatistics(@Param("voteIds") List<Long> voteIds);
}
