package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysEntryUserBo;
import org.housing.system.domain.vo.SysEntryUserVo;
import org.housing.system.service.ISysEntryUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报名人员
 *
 * <AUTHOR> Bin
 * @date 2024-10-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/entryUser")
public class SysEntryUserController extends BaseController {

    private final ISysEntryUserService sysEntryUserService;

    /**
     * 查询报名人员列表
     */
    @SaCheckPermission("system:entryUser:list")
    @GetMapping("/list")
    public TableDataInfo<SysEntryUserVo> list(SysEntryUserBo bo, PageQuery pageQuery) {
        return sysEntryUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出报名人员列表
     */
    @SaCheckPermission("system:entryUser:export")
    @Log(title = "报名人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysEntryUserBo bo, HttpServletResponse response) {
        List<SysEntryUserVo> list = sysEntryUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "报名人员", SysEntryUserVo.class, response);
    }

    /**
     * 获取报名人员详细信息
     *
     * @param entryUserId 主键
     */
    @SaCheckPermission("system:entryUser:query")
    @GetMapping("/{entryUserId}")
    public R<SysEntryUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long entryUserId) {
        return R.ok(sysEntryUserService.queryById(entryUserId));
    }

    /**
     * 新增报名人员
     */
    @SaCheckPermission("system:entryUser:add")
    @Log(title = "报名人员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysEntryUserBo bo) {
        return toAjax(sysEntryUserService.insertByBo(bo));
    }

    /**
     * 修改报名人员
     */
    @SaCheckPermission("system:entryUser:edit")
    @Log(title = "报名人员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysEntryUserBo bo) {
        return toAjax(sysEntryUserService.updateByBo(bo));
    }

    /**
     * 删除报名人员
     *
     * @param entryUserIds 主键串
     */
    @SaCheckPermission("system:entryUser:remove")
    @Log(title = "报名人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{entryUserIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] entryUserIds) {
        return toAjax(sysEntryUserService.deleteWithValidByIds(List.of(entryUserIds), true));
    }
}
