package org.housing.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.housing.common.excel.annotation.ExcelDictFormat;
import org.housing.common.excel.annotation.ExcelHousingFormat;
import org.housing.common.excel.convert.ExcelDictConvert;
import org.housing.common.excel.convert.ExcelHousingConvert;

import java.io.Serial;
import java.io.Serializable;

/**
 * 议题导入电话投票数据
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@NoArgsConstructor
public class SysTopicGovImportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 小区ID
     */
    @ExcelProperty(value = "小区(下拉选择)", converter = ExcelHousingConvert.class)
    @ExcelHousingFormat
    @NotNull(message = "小区不能为空")
    private Long housingId;

    /**
     * 用户账号
     */
    @ExcelProperty(value = "业主名(不能带空格和换行)")
    @NotBlank(message = "业主名不能为空")
    @Size(min = 2, max = 30, message = "业主名长度不能超过{max}个字符")
    private String ownerName;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码(不能带空格和换行)")
    @NotBlank(message = "手机号码不能为空")
    @Size(min = 11, max = 11, message = "手机号码长度不能大于小于{max}个字符")
    private String ownerPhone;

    /**
     * 投票项
     */
    @ExcelProperty(value = "投票项(不能带空格和换行)")
    @NotBlank(message = "投票项不能为空")
    private String voteName;

    @ExcelProperty(value = "意见(下拉选择)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_vote_status")
    private String advice;
}
