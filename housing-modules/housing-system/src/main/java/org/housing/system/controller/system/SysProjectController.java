package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysProjectBo;
import org.housing.system.domain.vo.SysProjectVo;
import org.housing.system.service.ISysProjectService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目工程
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/project")
public class SysProjectController extends BaseController {

    private final ISysProjectService sysProjectService;

    /**
     * 查询项目工程列表
     */
    @SaCheckPermission("system:project:list")
    @GetMapping("/list")
    public TableDataInfo<SysProjectVo> list(SysProjectBo bo, PageQuery pageQuery) {
        return sysProjectService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询项目工程下拉框列表
     */
    @SaCheckPermission("system:project:list")
    @GetMapping("/select")
    public R<List<SysProjectVo>> selectList() {
        return R.ok(sysProjectService.selectList());
    }

    /**
     * 导出项目工程列表
     */
    @SaCheckPermission("system:project:export")
    @Log(title = "项目工程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysProjectBo bo, HttpServletResponse response) {
        List<SysProjectVo> list = sysProjectService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目工程", SysProjectVo.class, response);
    }

    /**
     * 获取项目工程详细信息
     *
     * @param projectId 主键
     */
    @SaCheckPermission("system:project:query")
    @GetMapping("/{projectId}")
    public R<SysProjectVo> getInfo(@PathVariable Long projectId) {
        return R.ok(sysProjectService.queryById(projectId));
    }

    /**
     * 新增项目工程
     */
    @SaCheckPermission("system:project:add")
    @Log(title = "项目工程", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysProjectBo bo) {
        return toAjax(sysProjectService.insertByBo(bo));
    }

    /**
     * 修改项目工程
     */
    @SaCheckPermission("system:project:edit")
    @Log(title = "项目工程", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysProjectBo bo) {
        return toAjax(sysProjectService.updateByBo(bo));
    }

    /**
     * 删除项目工程
     *
     * @param projectId 主键串
     */
    @SaCheckPermission("system:project:remove")
    @Log(title = "项目工程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectId}")
    public R<Void> remove(@PathVariable Long projectId) {
        return toAjax(sysProjectService.deleteById(projectId));
    }
}
