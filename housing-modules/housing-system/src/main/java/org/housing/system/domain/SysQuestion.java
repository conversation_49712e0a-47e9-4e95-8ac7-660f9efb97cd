package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 问卷管理对象 sys_question
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_question")
public class SysQuestion extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 问卷id
     */
    @TableId(value = "question_id")
    private Long questionId;

    /**
     * 问卷标题
     */
    private String questionTitle;

    /**
     * 问卷描述
     */
    private String questionRemark;

    /**
     * 是否需要签名
     */
    private Long signature;

    /**
     * 关联的小区id
     */
    private Long housingId;

    /**
     * 题目选项id 英文逗号隔开
     */
    private String options;

    /**
     * 问卷开始时间
     */
    private Date questionStartTime;

    /**
     * 问卷结束时间
     */
    private Date questionEndTime;
}
