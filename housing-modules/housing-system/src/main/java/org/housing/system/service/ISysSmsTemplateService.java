package org.housing.system.service;

import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.bo.SysSmsTemplateBo;
import org.housing.system.domain.vo.SysSmsTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 短信配置Service接口
 *
 * <AUTHOR> Bin
 * @date 2025-03-13
 */
public interface ISysSmsTemplateService {

    /**
     * 查询短信配置
     *
     * @param smsId 主键
     * @return 短信配置
     */
    SysSmsTemplateVo queryById(Long smsId);

    /**
     * 分页查询短信配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 短信配置分页列表
     */
    TableDataInfo<SysSmsTemplateVo> queryPageList(SysSmsTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的短信配置列表
     *
     * @param bo 查询条件
     * @return 短信配置列表
     */
    List<SysSmsTemplateVo> queryList(SysSmsTemplateBo bo);

    /**
     * 新增短信配置
     *
     * @param bo 短信配置
     * @return 是否新增成功
     */
    Boolean insertByBo(SysSmsTemplateBo bo);

    /**
     * 修改短信配置
     *
     * @param bo 短信配置
     * @return 是否修改成功
     */
    Boolean updateByBo(SysSmsTemplateBo bo);

    /**
     * 校验并批量删除短信配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    SysSmsTemplateVo queryByCode(String code);


}
