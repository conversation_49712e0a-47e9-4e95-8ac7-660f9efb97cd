package org.housing.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.constant.CacheNames;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.redis.utils.CacheUtils;
import org.housing.system.domain.SysSmsChannel;
import org.housing.system.domain.bo.SysSmsChannelBo;
import org.housing.system.domain.vo.SysSmsChannelVo;
import org.housing.system.mapper.SysSmsChannelMapper;
import org.housing.system.service.ISysSmsChannelService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 短信渠道Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2025-03-13
 */
@RequiredArgsConstructor
@Service
public class SysSmsChannelServiceImpl implements ISysSmsChannelService {

    private final SysSmsChannelMapper baseMapper;

    /**
     * 查询短信渠道
     *
     * @param channelId 主键
     * @return 短信渠道
     */
    @Cacheable(cacheNames = CacheNames.SYS_SMS_CHANNEL, key = "#channelId")
    @Override
    public SysSmsChannelVo queryById(Long channelId){
        return baseMapper.selectVoById(channelId);
    }

    /**
     * 分页查询短信渠道列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 短信渠道分页列表
     */
    @Override
    public TableDataInfo<SysSmsChannelVo> queryPageList(SysSmsChannelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysSmsChannel> lqw = buildQueryWrapper(bo);
        Page<SysSmsChannelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的短信渠道列表
     *
     * @param bo 查询条件
     * @return 短信渠道列表
     */
    @Override
    public List<SysSmsChannelVo> queryList(SysSmsChannelBo bo) {
        LambdaQueryWrapper<SysSmsChannel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysSmsChannel> buildQueryWrapper(SysSmsChannelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysSmsChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getChannelCode()), SysSmsChannel::getChannelCode, bo.getChannelCode());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelSign()), SysSmsChannel::getChannelSign, bo.getChannelSign());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelAppId()), SysSmsChannel::getChannelAppId, bo.getChannelAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelSecret()), SysSmsChannel::getChannelSecret, bo.getChannelSecret());
        return lqw;
    }

    /**
     * 新增短信渠道
     *
     * @param bo 短信渠道
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysSmsChannelBo bo) {
        SysSmsChannel add = MapstructUtils.convert(bo, SysSmsChannel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setChannelId(add.getChannelId());
        }
        return flag;
    }

    /**
     * 修改短信渠道
     *
     * @param bo 短信渠道
     * @return 是否修改成功
     */
    @CacheEvict(cacheNames = CacheNames.SYS_SMS_CHANNEL, key = "#bo.channelId")
    @Override
    public Boolean updateByBo(SysSmsChannelBo bo) {
        SysSmsChannel update = MapstructUtils.convert(bo, SysSmsChannel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysSmsChannel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除短信渠道信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        for (Long id : ids) {
            CacheUtils.evict(CacheNames.SYS_SMS_CHANNEL, id);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
