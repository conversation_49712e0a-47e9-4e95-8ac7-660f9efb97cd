package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.excel.core.ExcelResult;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysOwnerBo;
import org.housing.system.domain.bo.UpdateAuditBo;
import org.housing.system.domain.vo.SysOwnerImportVo;
import org.housing.system.domain.vo.SysOwnerVo;
import org.housing.system.listener.SysOwnerImportListener;
import org.housing.system.service.ISysOwnerService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 业主
 *
 * <AUTHOR>
 * @date 2024-09-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/owner")
public class SysOwnerController extends BaseController {

    private final ISysOwnerService sysOwnerService;

    /**
     * 查询业主列表
     */
    @SaCheckPermission("system:owner:list")
    @GetMapping("/list")
    public TableDataInfo<SysOwnerVo> list(SysOwnerBo bo, PageQuery pageQuery) {
        return sysOwnerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出业主列表
     */
    @SaCheckPermission("system:owner:export")
    @Log(title = "业主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysOwnerBo bo, HttpServletResponse response) {
        List<SysOwnerVo> list = sysOwnerService.queryList(bo);
        ExcelUtil.exportExcel(list, "业主", SysOwnerVo.class, response);
    }

    /**
     * 获取业主详细信息
     *
     * @param userId 主键
     */
    @SaCheckPermission("system:owner:query")
    @GetMapping("/{userId}")
    public R<SysOwnerVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long userId) {
        return R.ok(sysOwnerService.queryById(userId));
    }

    /**
     * 新增业主
     */
    @SaCheckPermission("system:owner:add")
    @Log(title = "业主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOwnerBo bo) {
        return toAjax(sysOwnerService.insertByBo(bo));
    }

    /**
     * 修改业主
     */
    @SaCheckPermission("system:owner:edit")
    @Log(title = "业主", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOwnerBo bo) {
        return toAjax(sysOwnerService.updateByBo(bo));
    }

    /**
     * 删除业主
     *
     * @param userIds 主键串
     */
    @SaCheckPermission("system:owner:remove")
    @Log(title = "业主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] userIds) {
        return toAjax(sysOwnerService.deleteWithValidByIds(List.of(userIds), true));
    }

    /**
     * 导入数据
     *
     * @param file 导入文件
     */
    @Log(title = "业主", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:owner:add")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<SysOwnerImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysOwnerImportVo.class, new SysOwnerImportListener());
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "业主数据", SysOwnerImportVo.class, response);
    }

    /**
     * 更新审核状态
     */
    @PutMapping("/updateAudit")
    public R<Void> updateAudit(@Validated @RequestBody UpdateAuditBo bo) {
        return toAjax(sysOwnerService.updateAudit(bo));
    }
}
