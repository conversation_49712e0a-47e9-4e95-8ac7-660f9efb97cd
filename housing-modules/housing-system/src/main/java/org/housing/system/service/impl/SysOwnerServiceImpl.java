package org.housing.system.service.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.constant.CacheNames;
import org.housing.common.core.constant.GlobalConstants;
import org.housing.common.core.domain.model.LoginUser;
import org.housing.common.core.domain.model.MiniAppLogin;
import org.housing.common.core.exception.ServiceException;
import org.housing.common.core.exception.user.CaptchaException;
import org.housing.common.core.exception.user.CaptchaExpireException;
import org.housing.common.core.exception.user.UserException;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.SpringUtils;
import org.housing.common.core.utils.StreamUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.redis.utils.CacheUtils;
import org.housing.common.redis.utils.RedisUtils;
import org.housing.common.satoken.utils.LoginHelper;
import org.housing.common.tenant.helper.TenantHelper;
import org.housing.system.domain.SysOwner;
import org.housing.system.domain.SysOwnerHouse;
import org.housing.system.domain.bo.SysOwnerBo;
import org.housing.system.domain.bo.UpdateAuditBo;
import org.housing.system.domain.vo.SysOwnerVo;
import org.housing.system.mapper.SysHousingMapper;
import org.housing.system.mapper.SysOwnerHouseMapper;
import org.housing.system.mapper.SysOwnerMapper;
import org.housing.system.service.ISysOwnerService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 业主Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-28
 */
@RequiredArgsConstructor
@Service
public class SysOwnerServiceImpl implements ISysOwnerService {

    private final SysOwnerMapper baseMapper;

    private final SysHousingMapper housingMapper;

    private final SysOwnerHouseMapper ownerHouseMapper;

    /**
     * 查询业主
     *
     * @param userId 主键
     * @return 业主
     */
    @Cacheable(cacheNames = CacheNames.SYS_OWNER, key = "#userId")
    @Override
    public SysOwnerVo queryById(Long userId){
        SysOwnerVo sysOwnerVo = baseMapper.selectOwnerById(userId);
        if (sysOwnerVo != null) {
            Long count = ownerHouseMapper.selectCount(new LambdaQueryWrapper<SysOwnerHouse>().eq(SysOwnerHouse::getOwnerId, sysOwnerVo.getOwnerId()));
            sysOwnerVo.setHouseNum(Math.toIntExact(count));
        }
        return sysOwnerVo;
    }

    /**
     * 分页查询业主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 业主分页列表
     */
    @Override
    public TableDataInfo<SysOwnerVo> queryPageList(SysOwnerBo bo, PageQuery pageQuery) {
        QueryWrapper<SysOwner> lqw = buildQueryWrapper(bo);
        Page<SysOwnerVo> result = baseMapper.selectPageOwnerList(pageQuery.build(), lqw);
        setHouseNum(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的业主列表
     *
     * @param bo 查询条件
     * @return 业主列表
     */
    @Override
    public List<SysOwnerVo> queryList(SysOwnerBo bo) {
        QueryWrapper<SysOwner> lqw = buildQueryWrapper(bo);
        List<SysOwnerVo> sysOwnerVos = baseMapper.selectOwnerList(lqw);
        setHouseNum(sysOwnerVos);
        return sysOwnerVos;
    }

    private QueryWrapper<SysOwner> buildQueryWrapper(SysOwnerBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<SysOwner> query = Wrappers.query();
        query.like(StringUtils.isNotBlank(bo.getOwnerName()), "o.owner_name", bo.getOwnerName());
        query.like(StringUtils.isNotBlank(bo.getOwnerPhone()), "o.owner_phone", bo.getOwnerPhone());
        query.eq(StringUtils.isNotBlank(bo.getBuilding()), "o.building", bo.getBuilding());
        query.eq(StringUtils.isNotBlank(bo.getCell()), "o.cell", bo.getCell());
        query.eq(StringUtils.isNotBlank(bo.getRoom()), "o.room", bo.getRoom());
        query.eq(StringUtils.isNotBlank(bo.getAcreage()), "o.acreage", bo.getAcreage());
        query.eq(ObjectUtil.isNotNull(bo.getHousingId()), "o.housing_id", bo.getHousingId());
        query.eq(StringUtils.isNotBlank(bo.getAudit()), "o.audit", bo.getAudit());
        query.eq(StringUtils.isNotBlank(bo.getOpenId()), "o.open_id", bo.getOpenId());
        query.between(params.get("beginTime") != null && params.get("endTime") != null,
            "o.create_time", params.get("beginTime"), params.get("endTime"));
        query.orderByDesc("o.create_time");
        return query;
    }

    /**
     * 新增业主
     *
     * @param bo 业主
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SysOwnerBo bo) {
        bo.setCell(StrUtil.replace(bo.getCell(), RegexPool.CHINESES, (matcher) -> ""));
        bo.setRoom(StrUtil.replace(bo.getRoom(), RegexPool.CHINESES, (matcher) -> ""));
        if (StrUtil.isBlank(bo.getCluster())) {
            bo.setCluster("0");
        }
        SysOwnerVo sysOwnerVo = selectOwner(bo.getHousingId(), bo.getOwnerName(), bo.getOwnerPhone());
        if (ObjectUtil.isNotNull(sysOwnerVo)) {
            SysOwnerHouse sysOwnerHouse = ownerHouseMapper.selectOne(Wrappers.<SysOwnerHouse>lambdaQuery()
                .eq(SysOwnerHouse::getOwnerId, sysOwnerVo.getOwnerId())
                .eq(SysOwnerHouse::getBuilding, bo.getBuilding())
                .eq(SysOwnerHouse::getCluster, bo.getCluster())
                .eq(SysOwnerHouse::getCell, bo.getCell())
                .eq(SysOwnerHouse::getRoom, bo.getRoom())
            );
            if (sysOwnerHouse != null) {
                throw new ServiceException("该小区下业主已存在");
            }
            // 新的房源插入数据库
            sysOwnerHouse = new SysOwnerHouse();
            setHouse(bo, sysOwnerHouse, sysOwnerVo.getOwnerId());

            return ownerHouseMapper.insert(sysOwnerHouse) > 0;

        }
        // 走到这里说明没有业主
        if (StrUtil.isBlank(bo.getAudit())) {
            bo.setAudit("1");
        }

        SysOwner add = MapstructUtils.convert(bo, SysOwner.class);

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            SysOwnerHouse sysOwnerHouse = new SysOwnerHouse();
            setHouse(bo, sysOwnerHouse, add.getOwnerId());
            ownerHouseMapper.insert(sysOwnerHouse);
        }
        return flag;
    }

    private void setHouse(SysOwnerBo bo, SysOwnerHouse sysOwnerHouse, Long ownerId) {
        sysOwnerHouse.setOwnerId(ownerId);
        sysOwnerHouse.setHousingId(bo.getHousingId());
        sysOwnerHouse.setAcreage(Double.valueOf(bo.getAcreage()));
        sysOwnerHouse.setBuilding(bo.getBuilding());
        sysOwnerHouse.setCluster(bo.getCluster());
        sysOwnerHouse.setCell(bo.getCell());
        sysOwnerHouse.setRoom(Long.valueOf(bo.getRoom()));
        sysOwnerHouse.setHomeLicenseZ(bo.getHomeLicenseZ());
    }

    /**
     * 修改业主
     *
     * @param bo 业主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysOwnerBo bo) {
        SysOwner update = MapstructUtils.convert(bo, SysOwner.class);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            CacheUtils.evict(CacheNames.SYS_OWNER, update.getOwnerId());
        }
        return flag;
    }

    /**
     * 校验并批量删除业主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        ids.forEach(id -> {
            SysOwnerVo sysOwnerVo = SpringUtils.getAopProxy(this).queryById(id);
            CacheUtils.evict(CacheNames.SYS_OWNER, id);
            if (sysOwnerVo != null) {
                CacheUtils.evict(CacheNames.SYS_OWNER_HOUSE_LIST, id);
            }
        });
        boolean flag = baseMapper.deleteByIds(ids) > 0;
        if (flag) {
            ownerHouseMapper.delete(Wrappers.<SysOwnerHouse>lambdaQuery()
                .in(SysOwnerHouse::getOwnerId, ids));
        }
        return flag;
    }

    @Override
    public Map<String, Object> miniappLogin(MiniAppLogin login) {
        String code = RedisUtils.getCacheObject(GlobalConstants.CAPTCHA_CODE_KEY + login.getOwnerPhone());
        if (StringUtils.isBlank(code)) {
            throw new CaptchaExpireException();
        }
        if (!code.equals(login.getSmsCode())) {
            throw new CaptchaException();
        }
        SysOwner sysOwner = TenantHelper.ignore(() -> baseMapper.selectOne(new LambdaQueryWrapper<SysOwner>()
            .eq(SysOwner::getOwnerPhone, login.getOwnerPhone())
            .eq(SysOwner::getOwnerName, login.getOwnerName())
            .eq(SysOwner::getHousingId, login.getHousingId()), false));
        if (sysOwner == null) {
            throw new UserException("user.not.exists", login.getOwnerName());
        }
        if ("0".equals(sysOwner.getAudit())) {
            throw new UserException("user.not.audit", login.getOwnerName());
        }
        LoginUser loginUser = buildLoginUser(sysOwner);
        SaLoginModel model = new SaLoginModel();
        model.setDevice(loginUser.getDeviceType());
        LoginHelper.login(loginUser, model);
        return Map.of("accessToken", StpUtil.getTokenValue(), "expireIn", StpUtil.getTokenTimeout());
    }

    @Override
    public SysOwnerVo selectOwner(Long housingId, String ownerName, String ownerPhone) {
        return baseMapper.selectVoOne(Wrappers.<SysOwner>lambdaQuery()
            .eq(SysOwner::getHousingId, housingId)
            .eq(SysOwner::getOwnerName, ownerName)
            .eq(SysOwner::getOwnerPhone, ownerPhone));
    }

    @Override
    public Boolean updateAudit(UpdateAuditBo bo) {
        return baseMapper.update(Wrappers.<SysOwner>lambdaUpdate().set(SysOwner::getAudit, bo.getAudit()).eq(SysOwner::getOwnerId, bo.getOwnerId())) > 0;
    }

    @DS("slave")
    @Override
    public SysOwner selectBySlave(Long ownerId) {
        return baseMapper.selectById(ownerId);
    }

    /**
     * 构建登录用户
     */
    public LoginUser buildLoginUser(SysOwner user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setTenantId(user.getTenantId());
        loginUser.setUserId(user.getOwnerId());
        loginUser.setUsername(user.getOwnerName());
        loginUser.setNickname(user.getOwnerName());
        loginUser.setUserType("sys_user");
        loginUser.setDeviceType("app");
        loginUser.setHousingId(user.getHousingId());
        return loginUser;
    }

    public void setHouseNum(List<SysOwnerVo> ownerVos) {
        if (CollUtil.isNotEmpty(ownerVos)) {
            List<SysOwnerHouse> sysOwnerHouses = ownerHouseMapper.selectList(Wrappers.<SysOwnerHouse>lambdaQuery()
                .in(SysOwnerHouse::getOwnerId, ownerVos.stream().map(SysOwnerVo::getOwnerId).toList()));
            if (CollUtil.isNotEmpty(sysOwnerHouses)) {
                Map<Long, List<SysOwnerHouse>> houseMap = StreamUtils.groupByKey(sysOwnerHouses, SysOwnerHouse::getOwnerId);
                ownerVos.forEach(r -> r.setHouseNum(houseMap.getOrDefault(r.getOwnerId(), new ArrayList<>()).size()));
            }
        }
    }
}
