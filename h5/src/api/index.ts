import {
    http
} from '@/utils/request';

/**
 * 登录获取token
 */
export function mpLogin(code) {
  return http.get(`/auth/mpLogin`, {
    params: { code },
  });
}

/**
 * 获取投票列表
 */
export function getList(pageSize = 10, pageNum = 0, housingName = '') {
  return http.get(`/wechat/projects`, {
    params: { pageSize, pageNum, housingName },
  });
}
/**
 * 获取投票列表
 */
export function getProjectDetail(projectId) {
  return http.get(`/wechat/projectDetail`, {
    params: { projectId },
  });
}

/**
 * 报名
 */
export function postProject(data) {
  return http.post(`/wechat/projectEntry`, data);
}

/**
 * 获取手机验证码
 */
export function getSmsCode(phonenumber) {
  return http.get(`/resource/sms/code`, {
    params: { phonenumber },
  });
}
