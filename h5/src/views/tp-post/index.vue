<template>
  <div class="page">
    <header class="header">
      <img src="@/assets/commonImg/bgTitle.jpg" />
    </header>

    <div class="tipContent">
      <div class="title">{{ data.detailInfo.noticeTitle }}</div>
      <div class="main" v-if="data.detailInfo.noticeContent">
        <div v-html="data.detailInfo.noticeContent" :class="[!data.isExpand ? 'html' : '']"></div>
        <div v-if="!data.isExpand" @click="data.isExpand = true" class="expand"
          >展开阅读全文

          <img src="@/assets/commonImg/back.png" class="downImg" />
        </div>
        <div v-else @click="data.isExpand = false" class="expand"
          >收起
          <img src="@/assets/commonImg/back.png" class="upImg" />
        </div>
      </div>
    </div>
    <div class="mainContent">
      <nut-form ref="formRef" :model-value="formData" :rules="data.rules">
        <nut-form-item label="公司名称" required prop="companyName">
          <nut-input v-model="formData.companyName" placeholder="请输入公司名称" type="text" />
        </nut-form-item>
        <nut-form-item label="企业信用代码" required prop="creditCode">
          <nut-input v-model="formData.creditCode" placeholder="请输入企业信用代码" type="text" />
        </nut-form-item>
        <nut-form-item label="联系人" required prop="manageUser">
          <nut-input v-model="formData.manageUser" placeholder="请输入联系人" type="text" />
        </nut-form-item>
        <nut-form-item label="联系电话" required prop="phoneNumber">
          <nut-input v-model="formData.phoneNumber" placeholder="请输入联系电话" type="text" />
        </nut-form-item>
        <nut-form-item label="验证码" required prop="smsCode">
          <div class="smstConent">
            <nut-input v-model="formData.smsCode" placeholder="请输入验证码" type="text" />
            <nut-button
              :style="{
                height: '26px',
              }"
              class="smsbtn"
              type="info"
              @click="sendSms"
              :disabled="data.canSendTime > 0"
            >
              {{ data.canSendTime > 0 ? `重发${data.canSendTime}秒` : '获取验证码' }}
            </nut-button>
          </div>
        </nut-form-item>
        <nut-form-item label="邮箱" required prop="email">
          <nut-textarea v-model="formData.email" placeholder="请输入邮箱" type="text" />
        </nut-form-item>
        <nut-form-item label="营业执照" required prop="license">
          <nut-uploader
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessLicense"
          />
        </nut-form-item>
        <nut-form-item label="法人授权" required prop="powerBook">
          <nut-uploader
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessPowerBook"
          />
        </nut-form-item>
        <nut-form-item label="法人身份证或委托人身份证（正面）" required prop="idCardZ">
          <nut-uploader
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessIdCardZ"
          />
        </nut-form-item>
        <nut-form-item label="法人身份证或委托人身份证（反面）" required prop="idCardF">
          <nut-uploader
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessIdCardF"
          />
        </nut-form-item>
        <nut-form-item label="其他材料" required prop="other">
          <nut-uploader
            class="content"
            url="https://housing-admin.ninthone.cn/prod-api/resource/oss/upload"
            accept="image/*"
            @success="uploadSuccessOther"
          />
        </nut-form-item>
      </nut-form>
      <div class="agree"
        ><nut-checkbox icon-size="25" v-model="data.agree"> 我已阅读并同意<span @click="baseClick">《免责声明》</span> </nut-checkbox></div
      >
    </div>
    <div class="buttonContent">
      <nut-button block type="warning" @click="submit">提交资料</nut-button>
    </div>
    <nut-divider class="myDivider"> {{ data.dividerText }} </nut-divider>
  </div>
</template>

<script lang="ts" setup name="HomePage">
import {
    createVNode,
    ref
} from 'vue';
import {
    showDialog
} from '@nutui/nutui';
import '@nutui/nutui/dist/packages/dialog/style';
import {
    getProjectDetail,
    getSmsCode,
    postProject
} from '@/api';
import {
    useRouter
} from 'vue-router';

const router = useRouter();
  const formRef = ref(null);

  const formData = ref({
    // 项目主键
    projectId: '',
    // 公司名称
    companyName: '',
    // 信用代码
    creditCode: '',
    // 联系人
    manageUser: '',
    // 联系电话
    phoneNumber: '',
    // 邮箱
    email: '',
    // 营业执照
    license: '',
    // 法人授权委托书
    powerBook: '',
    // sfz
    idCardZ: '',
    // sfz
    idCardF: '',
    // 其他材料
    other: '',
    // 验证码
    smsCode: '',
  });

  const data = reactive({
    rules: {
      companyName: [{ required: true, message: '请填写公司名称' }],
      creditCode: [{ required: true, message: '请填写信用代码' }],
      manageUser: [{ required: true, message: '请填写联系人' }],
      phoneNumber: [{ required: true, message: '请填写联系电话' }],
      email: [{ required: true, message: '请填写邮箱' }],
      license: [{ required: true, message: '请填写营业执照' }],
      powerBook: [{ required: true, message: '请填写法人授权委托书' }],
      idCardZ: [{ required: true, message: '请填写身份证正面' }],
      idCardF: [{ required: true, message: '请填写身份证反面' }],
      other: [{ required: true, message: '请填写其他材料' }],
      smsCode: [{ required: true, message: '请填写验证码' }],
    },
    dividerText: 'divider',
    agree: false,
    detailInfo: {},
    isExpand: false,
    canSendTime: 0,
    intervalTimer: null as any,
  });

  const onCancel = () => {
    data.agree = false;
  };
  const onOk = () => {
    data.agree = true;
  };
  const baseClick = () => {
    showDialog({
      title: '免责声明',
      content: createVNode(
        'span',
        { style: { color: 'red' } },
        '本人对所提交的上述材料做出承诺，所提供的所有证件、材料均为真实有效的，如有虚假或者造假，本人愿意承担一切法律责任。',
      ),
      onCancel,
      onOk,
    });
  };

  const submit = () => {
    // router.push('/');
    formRef.value?.validate().then(async ({ valid }) => {
      if (valid) {
        if (data.agree) {
          await postProject(formData.value);
          router.push('/');
        } else {
          baseClick();
        }
      }
    });
  };

  //方法:用来提取url参数
  const getUrlParams = () => {
    const url = new URL(location.href);
    const params = new URLSearchParams(url.search);
    const code = params.get('projectId');
    return code;
  };

  const getDetail = async () => {
    formData.value.projectId = getUrlParams();

    const res = await getProjectDetail(getUrlParams());

    data.detailInfo = res.data;
  };
  getDetail();

  const uploadSuccessLicense = (parpams) => {
    if (JSON.parse(parpams.responseText).code === 200) {
      formData.value.license = JSON.parse(parpams.responseText).data.ossId;
    }
  };
  const uploadSuccessPowerBook = (parpams) => {
    if (JSON.parse(parpams.responseText).code === 200) {
      formData.value.powerBook = JSON.parse(parpams.responseText).data.ossId;
    }
  };
  const uploadSuccessIdCardZ = (parpams) => {
    if (JSON.parse(parpams.responseText).code === 200) {
      formData.value.idCardZ = JSON.parse(parpams.responseText).data.ossId;
    }
  };
  const uploadSuccessIdCardF = (parpams) => {
    if (JSON.parse(parpams.responseText).code === 200) {
      formData.value.idCardF = JSON.parse(parpams.responseText).data.ossId;
    }
  };
  const uploadSuccessOther = (parpams) => {
    if (JSON.parse(parpams.responseText).code === 200) {
      formData.value.other = JSON.parse(parpams.responseText).data.ossId;
    }
  };

  const sendSms = async () => {
    if (!!formData.value.phoneNumber) {
      data.canSendTime = 60;
      data.intervalTimer = setInterval(() => {
        if (data.canSendTime > 0) {
          data.canSendTime--;
        } else {
          clearInterval(data.intervalTimer!);
        }
      }, 1000);
      await getSmsCode(formData.value.phoneNumber);
    }
  };
</script>

<style lang="scss">
  .page {
    position: relative;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 40px;

    img {
      width: 100%;
      // height: 90px;
    }
  }

  .tipContent {
    color: aliceblue;
    padding: 0 20px;
    font-size: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;

    .title {
      font-size: 40px;
      margin-bottom: 10px;
    }

    .main {
      .html {
        max-height: 100px;
        overflow: hidden;
      }

      .expand {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 30px;
        font-size: 26px;

        img {
          width: 25px;
          height: 25px;
          margin-left: 10px;
        }

        .downImg {
          margin-top: 2px;
          rotate: -90deg;
        }

        .upImg {
          margin-top: 8px;
          rotate: 90deg;
        }
      }
    }
  }

  .mainContent {
    padding: 30px;
    padding-bottom: 300px;

    .nut-form-item__label {
      width: 200px;
    }

    .smstConent {
      display: flex;

      .smsbtn {
        padding: 10px;
      }
    }

    .agree {
      margin-top: 40px;

      .nut-checkbox__label {
        color: aliceblue;

        span {
          color: rgb(0, 183, 255);
        }
      }
    }
  }

  .buttonContent {
    position: absolute;
    bottom: 100px;
    width: 100%;
    padding: 0 30px;
    box-sizing: border-box;
  }

  .myDivider {
    position: absolute;
    bottom: 10px;
    width: 100%;
  }
</style>
